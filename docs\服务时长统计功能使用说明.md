# 服务时长统计功能使用说明

## 功能概述

服务时长统计功能允许员工精确记录每个服务项目和增项服务的具体用时，系统会自动计算平均时长并更新到相应的服务配置中。

## 主要特性

1. **精细化时长记录**：支持主服务和增项服务的分别计时
2. **自动平均值计算**：基于最近10次记录自动更新平均时长
3. **多端数据查看**：员工端、用户端、管理端都有相应的查询接口
4. **数据完整性保障**：采用冗余存储确保历史记录完整

## 使用流程

### 员工端操作流程

1. **开始服务**
   - 员工在订单详情页面选择要开始的服务项目
   - 点击"开始服务"按钮
   - 系统记录开始时间

2. **结束服务**
   - 服务完成后点击"结束服务"按钮
   - 系统自动计算服务时长
   - 更新平均时长数据

3. **查看进行中的服务**
   - 员工可以查看当前正在进行的所有服务
   - 避免重复开始同一服务

### 用户端查看

1. **订单服务记录**
   - 用户可以查看自己订单的详细服务时长记录
   - 包括每个服务项目的开始/结束时间和用时

2. **服务汇总信息**
   - 查看订单的服务时长汇总
   - 了解总服务时间和各项服务分布

### 管理端统计

1. **服务时长记录查询**
   - 支持多维度筛选查询
   - 导出服务时长数据

2. **服务效率统计**
   - 查看各服务项目的平均时长
   - 分析服务效率趋势

3. **员工工作统计**
   - 统计员工的服务时长数据
   - 评估员工工作效率

## 数据库变更

### 新增表

```sql
CREATE TABLE `service_duration_records` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `orderId` int NOT NULL COMMENT '关联订单ID',
  `orderDetailId` int DEFAULT NULL COMMENT '关联订单详情ID',
  `additionalServiceOrderId` int DEFAULT NULL COMMENT '关联追加服务订单ID',
  `employeeId` int NOT NULL COMMENT '关联员工ID',
  `recordType` enum('main_service','additional_service') NOT NULL COMMENT '记录类型',
  `serviceId` int DEFAULT NULL COMMENT '关联服务ID（主服务时使用）',
  `serviceName` varchar(100) NOT NULL COMMENT '服务名称（冗余字段）',
  `additionalServiceId` int DEFAULT NULL COMMENT '关联增项服务ID（增项服务时使用）',
  `additionalServiceName` varchar(100) DEFAULT NULL COMMENT '增项服务名称（冗余字段）',
  `startTime` datetime DEFAULT NULL COMMENT '服务开始时间',
  `endTime` datetime DEFAULT NULL COMMENT '服务结束时间',
  `duration` int DEFAULT NULL COMMENT '服务时长（分钟）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_orderId` (`orderId`),
  KEY `idx_employeeId` (`employeeId`),
  KEY `idx_orderDetailId` (`orderDetailId`),
  KEY `idx_additionalServiceOrderId` (`additionalServiceOrderId`),
  KEY `idx_serviceId` (`serviceId`),
  KEY `idx_additionalServiceId` (`additionalServiceId`),
  KEY `idx_recordType` (`recordType`),
  KEY `idx_startTime` (`startTime`),
  KEY `idx_endTime` (`endTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务时长记录表';
```

## 部署步骤

1. **运行数据库迁移**
   ```bash
   npm run migration:run
   ```

2. **重启服务**
   ```bash
   npm run dev
   ```

3. **验证功能**
   - 检查新增的API接口是否正常
   - 测试员工端开始/结束服务功能
   - 验证平均时长计算是否正确

## 注意事项

1. **数据一致性**
   - 确保在服务开始前订单状态为"服务中"
   - 同一服务项目不能重复开始

2. **权限控制**
   - 员工只能操作自己负责的订单
   - 用户只能查看自己的订单记录

3. **性能考虑**
   - 平均时长计算采用异步更新
   - 大量数据查询时建议使用分页

4. **错误处理**
   - 网络异常时确保数据完整性
   - 提供友好的错误提示信息

## 后续优化建议

1. **实时通知**
   - 可以考虑添加WebSocket推送服务状态变更
   - 用户端实时显示服务进度

2. **数据分析**
   - 增加更多维度的统计分析
   - 提供服务效率趋势图表

3. **移动端适配**
   - 优化移动端操作体验
   - 支持扫码快速开始/结束服务

4. **自动化功能**
   - 基于GPS定位自动判断服务开始/结束
   - 智能推荐服务时长预估
