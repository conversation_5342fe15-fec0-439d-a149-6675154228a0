import { Provide, Inject } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';
import { ServiceDurationRecord } from '../entity/service-duration-record.entity';
import { Order } from '../entity/order.entity';
import { Op } from 'sequelize';

/**
 * 订单总时长计算服务
 */
@Provide()
export class OrderDurationCalculatorService {
  @Inject()
  logger: ILogger;

  /**
   * 计算并更新订单总服务时长
   * @param orderId 订单ID
   */
  async calculateAndUpdateOrderDuration(orderId: number): Promise<void> {
    try {
      // 查询该订单所有已完成的服务时长记录
      const records = await ServiceDurationRecord.findAll({
        where: {
          orderId,
          duration: { [Op.not]: null },
          endTime: { [Op.not]: null },
        },
      });

      if (records.length === 0) {
        this.logger.info(`订单 ${orderId} 暂无完成的服务时长记录`);
        return;
      }

      // 计算总时长（所有服务项目和增项服务的时长之和）
      const totalDuration = records.reduce((sum, record) => {
        return sum + (record.duration || 0);
      }, 0);

      // 获取最早开始时间和最晚结束时间
      const startTimes = records
        .filter(record => record.startTime)
        .map(record => record.startTime!);

      const endTimes = records
        .filter(record => record.endTime)
        .map(record => record.endTime!);

      const actualServiceStartTime =
        startTimes.length > 0
          ? new Date(Math.min(...startTimes.map(time => time.getTime())))
          : null;

      const actualServiceEndTime =
        endTimes.length > 0
          ? new Date(Math.max(...endTimes.map(time => time.getTime())))
          : null;

      // 计算实际服务跨度时长（从第一个服务开始到最后一个服务结束的总时间）
      let actualServiceSpanDuration = null;
      if (actualServiceStartTime && actualServiceEndTime) {
        actualServiceSpanDuration = Math.round(
          (actualServiceEndTime.getTime() - actualServiceStartTime.getTime()) /
            (1000 * 60)
        );
      }

      // 更新订单的服务时长信息
      await Order.update(
        {
          actualServiceStartTime,
          actualServiceEndTime,
          actualServiceDuration: totalDuration, // 所有服务项目的累计时长
          // 可以考虑添加一个新字段存储服务跨度时长
        },
        {
          where: { id: orderId },
        }
      );

      this.logger.info(
        `订单 ${orderId} 总服务时长更新完成: 累计时长=${totalDuration}分钟, 服务跨度=${actualServiceSpanDuration}分钟`
      );
    } catch (error) {
      this.logger.error(`计算订单 ${orderId} 总服务时长失败:`, error);
      throw error;
    }
  }

  /**
   * 获取订单服务时长统计信息
   * @param orderId 订单ID
   */
  async getOrderDurationStatistics(orderId: number) {
    const records = await ServiceDurationRecord.findAll({
      where: {
        orderId,
        duration: { [Op.not]: null },
      },
      include: [
        {
          model: require('../entity/service.entity').Service,
          attributes: ['id', 'serviceName'],
        },
        {
          model: require('../entity/additional-service.entity')
            .AdditionalService,
          attributes: ['id', 'name'],
        },
      ],
      order: [['startTime', 'ASC']],
    });

    // 按类型分组统计
    const mainServiceRecords = records.filter(
      r => r.recordType === 'main_service'
    );
    const additionalServiceRecords = records.filter(
      r => r.recordType === 'additional_service'
    );

    const mainServiceDuration = mainServiceRecords.reduce(
      (sum, r) => sum + (r.duration || 0),
      0
    );
    const additionalServiceDuration = additionalServiceRecords.reduce(
      (sum, r) => sum + (r.duration || 0),
      0
    );
    const totalDuration = mainServiceDuration + additionalServiceDuration;

    // 计算服务跨度时长
    const startTimes = records.filter(r => r.startTime).map(r => r.startTime!);
    const endTimes = records.filter(r => r.endTime).map(r => r.endTime!);

    let serviceSpanDuration = null;
    if (startTimes.length > 0 && endTimes.length > 0) {
      const earliestStart = new Date(
        Math.min(...startTimes.map(t => t.getTime()))
      );
      const latestEnd = new Date(Math.max(...endTimes.map(t => t.getTime())));
      serviceSpanDuration = Math.round(
        (latestEnd.getTime() - earliestStart.getTime()) / (1000 * 60)
      );
    }

    return {
      orderId,
      totalDuration, // 总累计时长（所有服务项目时长之和）
      serviceSpanDuration, // 服务跨度时长（从开始到结束的总时间）
      mainServiceDuration, // 主服务累计时长
      additionalServiceDuration, // 增项服务累计时长
      mainServiceCount: mainServiceRecords.length,
      additionalServiceCount: additionalServiceRecords.length,
      records: records.map(record => ({
        id: record.id,
        recordType: record.recordType,
        serviceName: record.serviceName,
        startTime: record.startTime,
        endTime: record.endTime,
        duration: record.duration,
        remark: record.remark,
      })),
    };
  }
}
