import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ILogger } from '@midwayjs/logger';
import { BaseService } from '../common/BaseService';
import { CustomError } from '../error/custom.error';
import {
  ServiceDurationRecord,
  ServiceDurationRecordType,
} from '../entity/service-duration-record.entity';
import { Order } from '../entity/order.entity';
import { OrderDetail } from '../entity/order-detail.entity';
import { Service } from '../entity/service.entity';
import { AdditionalService } from '../entity/additional-service.entity';
import { AdditionalServiceOrder } from '../entity/additional-service-order.entity';
import { Employee } from '../entity/employee.entity';
import { OrderStatus } from '../common/Constant';
import { Op } from 'sequelize';
import { OrderDurationCalculatorService } from './order-duration-calculator.service';

/**
 * 开始服务接口数据
 */
export interface StartServiceData {
  /** 订单ID */
  orderId: number;
  /** 订单详情ID（主服务时使用） */
  orderDetailId?: number;
  /** 追加服务订单ID（增项服务时使用） */
  additionalServiceOrderId?: number;
  /** 记录类型 */
  recordType: ServiceDurationRecordType;
  /** 服务ID（主服务时使用） */
  serviceId?: number;
  /** 增项服务ID（增项服务时使用） */
  additionalServiceId?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 结束服务接口数据
 */
export interface EndServiceData {
  /** 服务时长记录ID */
  recordId: number;
  /** 备注 */
  remark?: string;
}

@Provide()
export class ServiceDurationRecordService extends BaseService<ServiceDurationRecord> {
  @Inject()
  ctx: Context;

  @Inject()
  logger: ILogger;

  @Inject()
  orderDurationCalculatorService: OrderDurationCalculatorService;

  constructor() {
    super('服务时长记录');
  }

  getModel() {
    return ServiceDurationRecord;
  }

  /**
   * 开始服务
   */
  async startService(employeeId: number, data: StartServiceData) {
    const {
      orderId,
      orderDetailId,
      additionalServiceOrderId,
      recordType,
      serviceId,
      additionalServiceId,
      remark,
    } = data;

    // 验证订单状态
    const order = await Order.findByPk(orderId, {
      include: [Employee],
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    if (order.status !== OrderStatus.服务中) {
      throw new CustomError('只有服务中的订单才能开始服务');
    }

    if (order.employeeId !== employeeId) {
      throw new CustomError('无权限操作此订单');
    }

    // 检查是否已有未结束的服务记录
    const existingRecord = await ServiceDurationRecord.findOne({
      where: {
        orderId,
        employeeId,
        recordType,
        ...(orderDetailId && { orderDetailId }),
        ...(additionalServiceOrderId && { additionalServiceOrderId }),
        ...(serviceId && { serviceId }),
        ...(additionalServiceId && { additionalServiceId }),
        endTime: null,
      },
    });

    if (existingRecord) {
      throw new CustomError('该服务项目已开始，请先结束当前服务');
    }

    // 获取服务名称
    let serviceName = '';
    let additionalServiceName = '';

    if (recordType === ServiceDurationRecordType.MAIN_SERVICE && serviceId) {
      const service = await Service.findByPk(serviceId);
      if (!service) {
        throw new CustomError('服务不存在');
      }
      serviceName = service.serviceName;
    } else if (
      recordType === ServiceDurationRecordType.ADDITIONAL_SERVICE &&
      additionalServiceId
    ) {
      const additionalService = await AdditionalService.findByPk(
        additionalServiceId
      );
      if (!additionalService) {
        throw new CustomError('增项服务不存在');
      }

      // 检查是否需要统计时长
      if (!additionalService.needDurationTracking) {
        throw new CustomError('该增项服务不需要统计时长');
      }

      additionalServiceName = additionalService.name;
      serviceName = additionalService.name; // 统一使用serviceName字段
    } else {
      throw new CustomError('服务类型和服务ID不匹配');
    }

    // 创建服务时长记录
    const record = await ServiceDurationRecord.create({
      orderId,
      orderDetailId,
      additionalServiceOrderId,
      employeeId,
      recordType,
      serviceId,
      serviceName,
      additionalServiceId,
      additionalServiceName,
      startTime: new Date(),
      remark,
    });

    return record;
  }

  /**
   * 结束服务
   */
  async endService(employeeId: number, data: EndServiceData) {
    const { recordId, remark } = data;

    // 查找服务记录
    const record = await ServiceDurationRecord.findByPk(recordId, {
      include: [Order],
    });

    if (!record) {
      throw new CustomError('服务记录不存在');
    }

    if (record.employeeId !== employeeId) {
      throw new CustomError('无权限操作此服务记录');
    }

    if (record.endTime) {
      throw new CustomError('该服务已结束');
    }

    if (!record.startTime) {
      throw new CustomError('服务尚未开始');
    }

    const endTime = new Date();
    const duration = Math.round(
      (endTime.getTime() - record.startTime.getTime()) / (1000 * 60)
    );

    // 更新服务记录
    await record.update({
      endTime,
      duration,
      remark: remark || record.remark,
    });

    // 异步更新平均时长（不等待完成，避免影响主流程）
    this.updateAverageDuration(
      record.recordType,
      record.serviceId,
      record.additionalServiceId
    ).catch(error => {
      this.logger.error('更新平均时长失败:', error);
    });

    // 异步更新订单总服务时长
    this.orderDurationCalculatorService
      .calculateAndUpdateOrderDuration(record.orderId)
      .catch(error => {
        this.logger.error('更新订单总服务时长失败:', error);
      });

    return record;
  }

  /**
   * 更新服务平均时长
   */
  private async updateAverageDuration(
    recordType: ServiceDurationRecordType,
    serviceId?: number,
    additionalServiceId?: number
  ) {
    try {
      if (recordType === ServiceDurationRecordType.MAIN_SERVICE && serviceId) {
        // 查询最近10次该服务的时长记录
        const records = await ServiceDurationRecord.findAll({
          where: {
            recordType: ServiceDurationRecordType.MAIN_SERVICE,
            serviceId,
            duration: { [Op.not]: null },
          },
          order: [['createdAt', 'DESC']],
          limit: 10,
        });

        if (records.length > 0) {
          const avgDuration = Math.round(
            records.reduce((sum, record) => sum + (record.duration || 0), 0) /
              records.length
          );

          await Service.update({ avgDuration }, { where: { id: serviceId } });
        }
      } else if (
        recordType === ServiceDurationRecordType.ADDITIONAL_SERVICE &&
        additionalServiceId
      ) {
        // 查询最近10次该增项服务的时长记录
        const records = await ServiceDurationRecord.findAll({
          where: {
            recordType: ServiceDurationRecordType.ADDITIONAL_SERVICE,
            additionalServiceId,
            duration: { [Op.not]: null },
          },
          order: [['createdAt', 'DESC']],
          limit: 10,
        });

        if (records.length > 0) {
          const avgDuration = Math.round(
            records.reduce((sum, record) => sum + (record.duration || 0), 0) /
              records.length
          );

          await AdditionalService.update(
            { duration: avgDuration },
            { where: { id: additionalServiceId } }
          );
        }
      }
    } catch (error) {
      this.logger.error('更新平均时长失败:', error);
    }
  }

  /**
   * 查询服务时长记录列表
   */
  async getServiceDurationRecords(orderId: number, employeeId?: number) {
    const where: any = { orderId };
    if (employeeId) {
      where.employeeId = employeeId;
    }

    const records = await ServiceDurationRecord.findAll({
      where,
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
        {
          model: Service,
          attributes: ['id', 'serviceName', 'avgDuration'],
        },
        {
          model: AdditionalService,
          attributes: [
            'id',
            'name',
            'duration',
            'price',
            'needDurationTracking',
          ],
        },
        {
          model: OrderDetail,
          attributes: ['id', 'serviceName', 'petName', 'servicePrice'],
        },
        {
          model: AdditionalServiceOrder,
          attributes: ['id', 'totalFee', 'status', 'originalPrice'],
        },
      ],
      order: [['startTime', 'ASC']],
    });

    // 计算统计信息
    const totalRecords = records.length;
    const completedRecords = records.filter(record => record.endTime).length;
    const inProgressRecords = records.filter(
      record => record.startTime && !record.endTime
    ).length;

    // 按类型分组
    const mainServiceRecords = records.filter(
      r => r.recordType === 'main_service'
    );
    const additionalServiceRecords = records.filter(
      r => r.recordType === 'additional_service'
    );

    // 计算总时长
    const totalDuration = records
      .filter(r => r.duration)
      .reduce((sum, r) => sum + (r.duration || 0), 0);

    const mainServiceDuration = mainServiceRecords
      .filter(r => r.duration)
      .reduce((sum, r) => sum + (r.duration || 0), 0);

    const additionalServiceDuration = additionalServiceRecords
      .filter(r => r.duration)
      .reduce((sum, r) => sum + (r.duration || 0), 0);

    return {
      orderId,
      records,
      statistics: {
        totalRecords,
        completedRecords,
        inProgressRecords,
        totalDuration,
        mainServiceDuration,
        additionalServiceDuration,
        mainServiceCount: mainServiceRecords.length,
        additionalServiceCount: additionalServiceRecords.length,
      },
    };
  }

  /**
   * 获取员工当前进行中的服务
   */
  async getCurrentServices(employeeId: number) {
    const records = await ServiceDurationRecord.findAll({
      where: {
        employeeId,
        endTime: null,
      },
      include: [
        {
          model: Order,
          attributes: ['id', 'sn', 'status'],
          include: [
            {
              model: require('../entity/customer.entity').Customer,
              attributes: ['id', 'nickname', 'phone'],
            },
          ],
        },
        {
          model: Service,
          attributes: ['id', 'serviceName', 'avgDuration'],
        },
        {
          model: AdditionalService,
          attributes: [
            'id',
            'name',
            'duration',
            'price',
            'needDurationTracking',
          ],
        },
        {
          model: OrderDetail,
          attributes: ['id', 'serviceName', 'petName', 'servicePrice'],
        },
        {
          model: AdditionalServiceOrder,
          attributes: ['id', 'totalFee', 'status', 'originalPrice'],
        },
      ],
      order: [['startTime', 'ASC']],
    });

    // 计算每个服务已进行的时长
    const currentTime = new Date();
    const recordsWithDuration = records.map(record => {
      let currentDuration = 0;
      if (record.startTime) {
        currentDuration = Math.round(
          (currentTime.getTime() - record.startTime.getTime()) / (1000 * 60)
        );
      }

      return {
        ...record.toJSON(),
        currentDuration, // 当前已进行的时长（分钟）
        expectedDuration:
          record.recordType === 'main_service'
            ? record.service?.avgDuration
            : record.additionalService?.duration, // 预期时长
      };
    });

    return {
      employeeId,
      currentServices: recordsWithDuration,
      totalCurrentServices: records.length,
    };
  }
}
