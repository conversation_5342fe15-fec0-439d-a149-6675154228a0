import { Controller, Post, Get, Body, Param, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Inject } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';

import {
  ServiceDurationRecordService,
  StartServiceData,
  EndServiceData,
} from '../../service/service-duration-record.service';
import { ServiceDurationRecordType } from '../../entity/service-duration-record.entity';

/**
 * 开始服务DTO
 */
export class StartServiceDTO {
  orderId: number;
  orderDetailId?: number;
  additionalServiceOrderId?: number;
  recordType: ServiceDurationRecordType;
  serviceId?: number;
  additionalServiceId?: number;
  remark?: string;
}

/**
 * 结束服务DTO
 */
export class EndServiceDTO {
  recordId: number;
  remark?: string;
}

@Controller('/employee/service-duration')
export class EmployeeServiceDurationController {
  @Inject()
  ctx: Context;

  @Inject()
  serviceDurationRecordService: ServiceDurationRecordService;

  @Post('/start', { summary: '开始服务' })
  @Validate()
  async startService(@Body() body: StartServiceDTO) {
    const employeeId = this.ctx.state.user.id;

    const data: StartServiceData = {
      orderId: body.orderId,
      orderDetailId: body.orderDetailId,
      additionalServiceOrderId: body.additionalServiceOrderId,
      recordType: body.recordType,
      serviceId: body.serviceId,
      additionalServiceId: body.additionalServiceId,
      remark: body.remark,
    };

    return await this.serviceDurationRecordService.startService(
      employeeId,
      data
    );
  }

  @Post('/end', { summary: '结束服务' })
  @Validate()
  async endService(@Body() body: EndServiceDTO) {
    const employeeId = this.ctx.state.user.id;

    const data: EndServiceData = {
      recordId: body.recordId,
      remark: body.remark,
    };

    return await this.serviceDurationRecordService.endService(employeeId, data);
  }

  @Get('/records/:orderId', { summary: '查询订单服务时长记录' })
  async getOrderServiceRecords(@Param('orderId') orderId: number) {
    return await this.serviceDurationRecordService.getServiceDurationRecords(
      orderId
    );
  }

  @Get('/current', { summary: '获取当前进行中的服务' })
  async getCurrentServices() {
    const employeeId = this.ctx.state.user.id;
    return await this.serviceDurationRecordService.getCurrentServices(
      employeeId
    );
  }

  @Get('/my-records', { summary: '查询我的服务时长记录' })
  async getMyServiceRecords(@Query('orderId') orderId?: number) {
    const employeeId = this.ctx.state.user.id;

    if (orderId) {
      return await this.serviceDurationRecordService.getServiceDurationRecords(
        orderId,
        employeeId
      );
    }

    // 如果没有指定订单ID，返回当前进行中的服务
    return await this.serviceDurationRecordService.getCurrentServices(
      employeeId
    );
  }
}
