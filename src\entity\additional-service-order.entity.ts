import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
} from 'sequelize-typescript';
import {
  Customer,
  Employee,
  OrderDetail,
  AdditionalServiceOrderDetail,
  AdditionalServiceDiscountInfo,
  ServiceDurationRecord,
} from './';

/**
 * 追加服务订单状态枚举
 */
export enum AdditionalServiceOrderStatus {
  /** 待确认 */
  PENDING_CONFIRM = 'pending_confirm',
  /** 已确认 */
  CONFIRMED = 'confirmed',
  /** 已拒绝 */
  REJECTED = 'rejected',
  /** 待付款 */
  PENDING_PAYMENT = 'pending_payment',
  /** 已付款/服务中 */
  PAID = 'paid',
  /** 已完成 */
  COMPLETED = 'completed',
  /** 已取消 */
  CANCELLED = 'cancelled',
  /** 退款中 */
  REFUNDING = 'refunding',
  /** 已退款 */
  REFUNDED = 'refunded',
}

/**
 * 追加服务订单属性接口
 */
export interface AdditionalServiceOrderAttributes {
  /** 追加服务订单ID */
  id: number;
  /** 追加服务订单编号 */
  sn: string;
  /** 关联的订单详情ID */
  orderDetailId: number;
  /** 关联的客户ID */
  customerId: number;
  /** 关联的员工ID */
  employeeId?: number;
  /** 订单状态 */
  status: AdditionalServiceOrderStatus;
  /** 原价 */
  originalPrice: number;
  /** 实际支付金额 */
  totalFee: number;
  /** 权益卡抵扣金额 */
  cardDeduction: number;
  /** 代金券抵扣金额 */
  couponDeduction: number;
  /** 员工确认时间 */
  confirmTime?: Date;
  /** 支付时间 */
  payTime?: Date;
  /** 拒绝原因 */
  rejectReason?: string;
  /** 微信支付预支付ID */
  prepayId?: string;
  /** 备注 */
  remark?: string;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
  /** 关联的订单详情 */
  orderDetail?: OrderDetail;
  /** 关联的客户 */
  customer?: Customer;
  /** 关联的员工 */
  employee?: Employee;
  /** 追加服务明细列表 */
  details?: AdditionalServiceOrderDetail[];
  /** 优惠信息列表 */
  discountInfos?: AdditionalServiceDiscountInfo[];
}

/**
 * 追加服务订单表
 */
@Table({
  tableName: 'additional_service_orders',
  timestamps: true,
  comment: '追加服务订单表',
})
export class AdditionalServiceOrder
  extends Model<AdditionalServiceOrderAttributes>
  implements AdditionalServiceOrderAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '追加服务订单ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(30),
    allowNull: false,
    unique: true,
    comment: '追加服务订单编号',
  })
  sn: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联的订单详情ID',
  })
  @ForeignKey(() => OrderDetail)
  orderDetailId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联的客户ID',
  })
  @ForeignKey(() => Customer)
  customerId: number;

  @Column({
    type: DataType.INTEGER,
    comment: '关联的员工ID',
  })
  @ForeignKey(() => Employee)
  employeeId?: number;

  @Column({
    type: DataType.ENUM(...Object.values(AdditionalServiceOrderStatus)),
    allowNull: false,
    defaultValue: AdditionalServiceOrderStatus.PENDING_CONFIRM,
    comment: '订单状态',
  })
  status: AdditionalServiceOrderStatus;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '原价',
  })
  originalPrice: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '实际支付金额',
  })
  totalFee: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    defaultValue: 0.0,
    comment: '权益卡抵扣金额',
  })
  cardDeduction: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    defaultValue: 0.0,
    comment: '代金券抵扣金额',
  })
  couponDeduction: number;

  @Column({
    type: DataType.DATE,
    comment: '员工确认时间',
  })
  confirmTime?: Date;

  @Column({
    type: DataType.DATE,
    comment: '支付时间',
  })
  payTime?: Date;

  @Column({
    type: DataType.STRING(255),
    comment: '拒绝原因',
  })
  rejectReason?: string;

  @Column({
    type: DataType.STRING(100),
    comment: '微信支付预支付ID',
  })
  prepayId?: string;

  @Column({
    type: DataType.STRING(255),
    comment: '备注',
  })
  remark?: string;

  @BelongsTo(() => OrderDetail)
  orderDetail?: OrderDetail;

  @BelongsTo(() => Customer)
  customer?: Customer;

  @BelongsTo(() => Employee)
  employee?: Employee;

  @HasMany(() => AdditionalServiceOrderDetail)
  details?: AdditionalServiceOrderDetail[];

  @HasMany(() => AdditionalServiceDiscountInfo)
  discountInfos?: AdditionalServiceDiscountInfo[];

  @HasMany(() => ServiceDurationRecord)
  serviceDurationRecords?: ServiceDurationRecord[];
}
