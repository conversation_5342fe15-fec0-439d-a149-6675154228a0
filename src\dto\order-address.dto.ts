import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 更新订单服务地址DTO
 */
export class UpdateOrderAddressDto {
  @Rule(
    RuleType.string()
      .optional()
      .max(255)
      .error(new Error('服务地址不能超过255字符'))
  )
  /** 服务地址 */
  address?: string;

  @Rule(
    RuleType.string()
      .optional()
      .max(255)
      .error(new Error('服务地址详情不能超过255字符'))
  )
  /** 服务地址详情 */
  addressDetail?: string;

  @Rule(
    RuleType.number()
      .optional()
      .min(-180)
      .max(180)
      .error(new Error('经度必须在-180到180之间'))
  )
  /** 服务地址经度 */
  longitude?: number;

  @Rule(
    RuleType.number()
      .optional()
      .min(-90)
      .max(90)
      .error(new Error('纬度必须在-90到90之间'))
  )
  /** 服务地址纬度 */
  latitude?: number;

  @Rule(
    RuleType.string()
      .optional()
      .max(255)
      .error(new Error('服务地址备注不能超过255字符'))
  )
  /** 服务地址备注 */
  addressRemark?: string;

  @Rule(
    RuleType.number()
      .integer()
      .optional()
      .allow(null)
      .error(new Error('地址ID必须是整数'))
  )
  /** 地址ID（可选，如果提供则关联到客户地址，null表示取消关联） */
  addressId?: number | null;

  @Rule(
    RuleType.number().integer().optional().error(new Error('员工ID必须是整数'))
  )
  /** 员工ID（用于记录操作日志） */
  employeeId?: number;

  @Rule(
    RuleType.string()
      .optional()
      .valid('admin', 'employee', 'customer')
      .default('admin')
      .error(new Error('用户类型必须是admin、employee或customer'))
  )
  /** 用户类型（用于权限控制） */
  userType?: 'admin' | 'employee' | 'customer';
}
